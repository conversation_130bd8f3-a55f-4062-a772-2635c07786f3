const nodemailer = require('nodemailer');

const transporter = nodemailer.createTransport({
    service: 'gmail',
    auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS,
    },
});

const sendSupervisorInvite  = async (email,otp,name,loginUrl) => {
    const mailOptions = {
        from: '"DIT IPTMS" <<EMAIL>>',
        to: email,
        subject: "Your Supervisor Account",
        html: `
        <p>Hello, ${name}</p>
        <p>Your supervisor account has been created.</p>
        <p><strong>One-Time Password (OTP):</strong> ${otp}</p>
        <p><a href="${loginUrl}">Click here to login</a></p>
        <p>After logging in, you will be prompted to change your password.</p>
        `,
    }

    try {
        const info = await transporter.sendMail(mailOptions);
        console.log("Email sent: ", info.response);
    } catch (err) {
         console.error("Failed to send email:", err);
    }}

module.exports = { sendSupervisorInvite };
