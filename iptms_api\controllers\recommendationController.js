const Recommendation = require('../models/Recommendation');

const getAllRecommendations = async (req, res) => {
    try {
        const result = await Recommendation.index();

        return res.status(200).json({
            success: true,
            data: result 
        });

    } catch (err) {
        console.error(err.message);
        return res.status(500).json({
            success: false,
            message: 'Internal server error.'
        });
    }
}

const getSingleRecommendation = async (req, res) => {
    const id = req.params.id;
    
    try {
        const result = await Recommendation.show(id);

        return res.status(200).json({
            success: true,
            data: result 
        });

    } catch (err) {
        console.error(err.message);
        return res.status(500).json({
            success: false,
            message: 'Internal server error.'
        });
    }
}

const getStudentRecommendations = async (req, res) => {
    const student_id = req.params.student_id;
    
    try {
        const result = await Recommendation.getByStudent(student_id);

        return res.status(200).json({
            success: true,
            data: result 
        });

    } catch (err) {
        console.error(err.message);
        return res.status(500).json({
            success: false,
            message: 'Internal server error.'
        });
    }
}

const getSupervisorRecommendations = async (req, res) => {
    const supervisor_id = req.params.supervisor_id;
    
    try {
        const result = await Recommendation.getBySupervisor(supervisor_id);

        return res.status(200).json({
            success: true,
            data: result 
        });

    } catch (err) {
        console.error(err.message);
        return res.status(500).json({
            success: false,
            message: 'Internal server error.'
        });
    }
}

const createRecommendation = async (req, res) => {
    const data = req.body;

    try {
        // If student_id is not provided, try to get it from user context or data
        if (!data.student_id && data.user_id) {
            data.student_id = data.user_id;
        }

        // If still no student_id, return error
        if (!data.student_id) {
            return res.status(400).json({
                success: false,
                message: 'Student ID is required'
            });
        }

        // If supervisor_id is not provided, return error
        if (!data.supervisor_id) {
            return res.status(400).json({
                success: false,
                message: 'Supervisor ID is required'
            });
        }

        const result = await Recommendation.create(data);

        return res.status(200).json({
            success: true,
            message: 'Recommendation created successfully.',
            data: result
        });

    } catch (err) {
        console.error(err.message);
        return res.status(500).json({
            success: false,
            message: 'Internal server error.'
        });
    }
}

const updateRecommendation = async (req, res) => {
    const id = req.params.id;
    const data = req.body;
    
    try {
        const result = await Recommendation.update(id, data);

        return res.status(200).json({
            success: true,
            message: 'Recommendation updated successfully.',
            data: result 
        });

    } catch (err) {
        console.error(err.message);
        return res.status(500).json({
            success: false,
            message: 'Internal server error.'
        });
    }
}

const deleteRecommendation = async (req, res) => {
    const id = req.params.id;
    
    try {
        const result = await Recommendation.delete(id);

        return res.status(200).json({
            success: true,
            message: 'Recommendation deleted successfully.',
            data: result 
        });

    } catch (err) {
        console.error(err.message);
        return res.status(500).json({
            success: false,
            message: 'Internal server error.'
        });
    }
}

const markRecommendationAsRead = async (req, res) => {
    const id = req.params.id;
    
    try {
        const result = await Recommendation.update(id, {
            is_read: 1,
            read_at: new Date()
        });

        return res.status(200).json({
            success: true,
            message: 'Recommendation marked as read.',
            data: result 
        });

    } catch (err) {
        console.error(err.message);
        return res.status(500).json({
            success: false,
            message: 'Internal server error.'
        });
    }
}

const updateRecommendationStatus = async (req, res) => {
    const id = req.params.id;
    const { status, response } = req.body;
    
    try {
        const updateData = { status };
        if (response) {
            updateData.response = response;
        }

        const result = await Recommendation.update(id, updateData);

        return res.status(200).json({
            success: true,
            message: 'Recommendation status updated successfully.',
            data: result 
        });

    } catch (err) {
        console.error(err.message);
        return res.status(500).json({
            success: false,
            message: 'Internal server error.'
        });
    }
}

const respondToRecommendation = async (req, res) => {
    const id = req.params.id;
    const { response } = req.body;
    
    try {
        const result = await Recommendation.update(id, {
            response: response,
            status: 'acknowledged'
        });

        return res.status(200).json({
            success: true,
            message: 'Response added to recommendation successfully.',
            data: result 
        });

    } catch (err) {
        console.error(err.message);
        return res.status(500).json({
            success: false,
            message: 'Internal server error.'
        });
    }
}

// Get recommendations with role-based filtering
const getRecommendationsWithFilter = async (req, res) => {
    const { user_id, role } = req.user; // From JWT middleware
    const { category, priority, status } = req.query;
    
    try {
        let result;
        
        if (role === 'student') {
            result = await Recommendation.getByStudent(user_id);
        } else if (role === 'supervisor') {
            result = await Recommendation.getBySupervisor(user_id);
        } else {
            result = await Recommendation.index();
        }

        // Apply additional filters if provided
        if (category) {
            result = result.filter(rec => rec.category === category);
        }
        if (priority) {
            result = result.filter(rec => rec.priority === priority);
        }
        if (status) {
            result = result.filter(rec => rec.status === status);
        }

        return res.status(200).json({
            success: true,
            data: result 
        });

    } catch (err) {
        console.error(err.message);
        return res.status(500).json({
            success: false,
            message: 'Internal server error.'
        });
    }
}

module.exports = {
    getAllRecommendations,
    getSingleRecommendation,
    getStudentRecommendations,
    getSupervisorRecommendations,
    createRecommendation,
    updateRecommendation,
    deleteRecommendation,
    markRecommendationAsRead,
    updateRecommendationStatus,
    respondToRecommendation,
    getRecommendationsWithFilter
}
