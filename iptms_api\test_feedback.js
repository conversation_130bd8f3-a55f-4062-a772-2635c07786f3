const axios = require('axios');

async function testFeedback() {
    try {
        console.log('Testing feedback creation...');
        
        const feedbackData = {
            student_id: 16, // Using the existing student ID from the database
            category: 'general',
            title: 'Test Feedback',
            submission_type: 'write',
            content: 'This is a test feedback content',
            character_count: 35,
            rating: 5,
            is_submitted: false
        };

        const response = await axios.post('http://localhost:8080/api/reports/feedback', feedbackData);
        
        console.log('Response:', response.data);
        
        if (response.data.success) {
            console.log('✅ Feedback created successfully!');
            console.log('Created feedback ID:', response.data.data.id);
        } else {
            console.log('❌ Failed to create feedback');
        }
        
    } catch (error) {
        console.error('❌ Error testing feedback:', error.response?.data || error.message);
    }
}

testFeedback();
