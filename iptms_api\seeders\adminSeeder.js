const db = require('../config/db');
const bcrypt = require('bcryptjs'); 

const adminSeeder = async () => {
    try {
        const email = '<EMAIL>';
        const password = '#Admin123';

        // Step 1: Hash the password
        const hashedPassword = password;
        // const hashedPassword = await bcrypt.hash(password, 10);

        // Step 2: Insert user into the users table
        const [userResult] = await db.execute('INSERT INTO users (email, password) VALUES (?, ?)', [email, hashedPassword]);

        const userId = userResult.insertId; 
        console.log(`${userResult.affectedRows} row(s) inserted into users table.`);

        // Step 3: Insert roles and assign them to the user
        const roles = ['admin', 'student', 'supervisor'];

        // Insert roles and assign 'admin' to the user
        const rolePromises = roles.map(async (role) => {
            const [roleResult] = await db.execute('INSERT INTO roles (name) VALUES (?)', [role]);
            const roleId = roleResult.insertId;
            console.log(`${roleResult.affectedRows} row(s) inserted into roles table for role ${role}.`);

            // Assign 'admin' role to the user
            if (role === 'admin') {
                await db.execute('INSERT INTO user_has_role (user_id, role_id) VALUES (?, ?)', [userId, roleId]);
                console.log(`Admin user with ID ${userId} assigned to role ${role} (Role ID: ${roleId})`);
            }
        });

        // Wait for both user insertion and role assignments to finish
        await Promise.all(rolePromises);

        console.log('Seeding completed!');
    } catch (err) {
        console.error('Error during seeding process: ', err);
    } finally {
        db.end();
    }
};

adminSeeder();