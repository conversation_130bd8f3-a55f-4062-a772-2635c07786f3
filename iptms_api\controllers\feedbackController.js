const Feedback = require('../models/Feedback');

const getAllFeedback = async (req, res) => {
    try {
        const result = await Feedback.index();

        return res.status(200).json({
            success: true,
            data: result 
        });

    } catch (err) {
        console.error(err.message);
        return res.status(500).json({
            success: false,
            message: 'Internal server error.'
        });
    }
}

const getSingleFeedback = async (req, res) => {
    const id = req.params.id;
    
    try {
        const result = await Feedback.show(id);

        return res.status(200).json({
            success: true,
            data: result 
        });

    } catch (err) {
        console.error(err.message);
        return res.status(500).json({
            success: false,
            message: 'Internal server error.'
        });
    }
}

const getStudentFeedback = async (req, res) => {
    const student_id = req.params.student_id;
    
    try {
        const result = await Feedback.getByStudent(student_id);

        return res.status(200).json({
            success: true,
            data: result 
        });

    } catch (err) {
        console.error(err.message);
        return res.status(500).json({
            success: false,
            message: 'Internal server error.'
        });
    }
}

const getFeedbackByType = async (req, res) => {
    const feedback_type = req.params.type;
    
    try {
        const result = await Feedback.getByType(feedback_type);

        return res.status(200).json({
            success: true,
            data: result 
        });

    } catch (err) {
        console.error(err.message);
        return res.status(500).json({
            success: false,
            message: 'Internal server error.'
        });
    }
}

const createFeedback = async (req, res) => {
    const data = req.body;

    try {
        // If student_id is not provided, try to get it from user context or data
        if (!data.student_id && data.user_id) {
            data.student_id = data.user_id;
        }

        // If still no student_id, return error
        if (!data.student_id) {
            return res.status(400).json({
                success: false,
                message: 'Student ID is required'
            });
        }

        const result = await Feedback.create(data);

        return res.status(200).json({
            success: true,
            message: data.is_submitted ? 'Feedback submitted successfully.' : 'Feedback saved successfully.',
            data: result
        });

    } catch (err) {
        console.error(err.message);
        return res.status(500).json({
            success: false,
            message: 'Internal server error.'
        });
    }
}

const updateFeedback = async (req, res) => {
    const id = req.params.id;
    const data = req.body;
    
    try {
        const result = await Feedback.update(id, data);

        return res.status(200).json({
            success: true,
            message: data.is_submitted ? 'Feedback submitted successfully.' : 'Feedback updated successfully.',
            data: result 
        });

    } catch (err) {
        console.error(err.message);
        return res.status(500).json({
            success: false,
            message: 'Internal server error.'
        });
    }
}

const deleteFeedback = async (req, res) => {
    const id = req.params.id;
    
    try {
        const result = await Feedback.delete(id);

        return res.status(200).json({
            success: true,
            message: 'Feedback deleted successfully.',
            data: result 
        });

    } catch (err) {
        console.error(err.message);
        return res.status(500).json({
            success: false,
            message: 'Internal server error.'
        });
    }
}

// Route handler for frontend compatibility - matches Reports.ts API calls
const getFeedbackForReports = async (req, res) => {
    const { student_id } = req.query;
    
    try {
        let result;
        if (student_id) {
            result = await Feedback.getByStudent(student_id);
        } else {
            result = await Feedback.index();
        }

        return res.status(200).json({
            success: true,
            data: result 
        });

    } catch (err) {
        console.error(err.message);
        return res.status(500).json({
            success: false,
            message: 'Internal server error.'
        });
    }
}

// Route handler for saving feedback from frontend - matches Reports.ts API calls
const saveFeedbackForReports = async (req, res) => {
    const data = req.body;

    try {
        // If student_id is not provided, try to get it from user context or data
        if (!data.student_id && data.user_id) {
            data.student_id = data.user_id;
        }

        // If still no student_id, return error
        if (!data.student_id) {
            return res.status(400).json({
                success: false,
                message: 'Student ID is required'
            });
        }

        let result;
        if (data.id) {
            // Update existing feedback
            result = await Feedback.update(data.id, data);
        } else {
            // Create new feedback
            result = await Feedback.create(data);
        }

        console.log('Feedback operation result:', result);

        const message = data.is_submitted
            ? "Feedback submitted successfully"
            : "Feedback saved successfully";

        return res.status(200).json({
            success: true,
            message: message,
            data: result
        });

    } catch (err) {
        console.error(err.message);
        const message = data.is_submitted
            ? "Failed to submit feedback"
            : "Failed to save feedback";
        return res.status(500).json({
            success: false,
            message: message
        });
    }
}

const updateFeedbackStatus = async (req, res) => {
    const id = req.params.id;
    const { admin_reviewed, admin_response } = req.body;
    
    try {
        const result = await Feedback.update(id, {
            admin_reviewed: admin_reviewed,
            admin_response: admin_response
        });

        return res.status(200).json({
            success: true,
            message: 'Feedback status updated successfully.',
            data: result 
        });

    } catch (err) {
        console.error(err.message);
        return res.status(500).json({
            success: false,
            message: 'Internal server error.'
        });
    }
}

module.exports = {
    getAllFeedback,
    getSingleFeedback,
    getStudentFeedback,
    getFeedbackByType,
    createFeedback,
    updateFeedback,
    deleteFeedback,
    getFeedbackForReports,
    saveFeedbackForReports,
    updateFeedbackStatus
}
