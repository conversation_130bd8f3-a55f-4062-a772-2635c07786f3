const db = require('../config/db');
require('dotenv').config();

const index = async () => {
    try {
        const [rows] = await db.query(`SELECT * from ipt ORDER BY created_at DESC`);

        return rows;
    } catch (err) {
        console.error(err.message);
        throw new Error('Error fetching IPTs');
    }
};

const create = async (name, user_id) => {
    try {
        const [rows] = await db.query(`
            SELECT * from ipt 
            WHERE name = ?
        `, [name]);

        if (rows.length > 0) {
            return { error: 'IPT already exists' };
        }

        const [result] = await db.query(`
            INSERT INTO ipt (name, created_by, created_at, updated_at)
            VALUES (?, ?, NOW(), NOW())
        `, [name, user_id]);

        if (result.affectedRows > 0) {
            return { 
                success: true,
                id: result.insertId 
            };
        } else {
            return { error: 'Failed to create IPT' };
        }
    } catch (err) {
        console.error(err.message);
        throw new Error('Error during IPT creation process');
    }
};

module.exports = {
   index, create
};
