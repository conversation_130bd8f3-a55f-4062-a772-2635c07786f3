'use client'

import { useState } from "react"

import { columns } from "@/features/logbook/components/columns"
import { DataTable } from "@/features/logbook/components/data-table"
import { DottedSeparator } from "@/components/dotted-separator"
import { WeekSelector } from "@/features/logbook/components/week-selector"
import { WeeklyReportEditor } from "@/features/logbook/components/weekly-report-editor"
import { DailyLogForm } from "@/features/logbook/components/daily-log-form"
import { ConfirmationDialog } from "@/features/logbook/components/confirmation-dialog"
import { LogDay, WeeklyReport } from "@/features/logbook/types/logbook"
import { useLogbook } from "@/features/logbook/hooks/use-logbook"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { PlusCircle, AlertCircle, CheckCircle2 } from "lucide-react"
import { toast } from "sonner"

export default function LogbookPage() {
  const {
    currentWeek,
    dailyLogs,
    weeklyReport,
    loading,
    saving,
    availableDays,
    nextDay,
    isWeekFull,
    changeWeek,
    createDailyLog,
    updateDailyLog,
    saveWeeklyReport
  } = useLogbook(1)

  const [showDailyLogForm, setShowDailyLogForm] = useState(false)
  const [editingLog, setEditingLog] = useState<LogDay | null>(null)
  const [showWeekFullDialog, setShowWeekFullDialog] = useState(false)

  const handleAddDailyLog = () => {
    if (isWeekFull) {
      setShowWeekFullDialog(true)
      return
    }

    setEditingLog(null)
    setShowDailyLogForm(true)
  }

  const handleEditDailyLog = (log: LogDay) => {
    setEditingLog(log)
    setShowDailyLogForm(true)
  }

  const handleSaveDailyLog = async (logData: LogDay) => {
    try {
      if (editingLog) {
        await updateDailyLog(editingLog.id, logData)
        toast.success("Daily log updated successfully")
      } else {
        await createDailyLog(logData)
        toast.success("Daily log created successfully")
      }
      setShowDailyLogForm(false)
      setEditingLog(null)
    } catch (error) {
      // Error handling is done in the hook
    }
  }

  const handleSaveReport = async (report: WeeklyReport) => {
    try {
      await saveWeeklyReport(report)
    } catch (error) {
      // Error handling is done in the hook
    }
  }

  const handleSubmitReport = async (report: WeeklyReport) => {
    try {
      await saveWeeklyReport({ ...report, is_submitted: true })
      toast.success("Report submitted successfully")
    } catch (error) {
      // Error handling is done in the hook
    }
  }

  return (
    <>
      <div className="h-full flex-1 flex-col space-y-4 p-4 md:space-y-8 md:p-8 flex">
        <div className="flex items-center justify-between space-y-2">
          <div>
            <h2 className="text-2xl font-bold tracking-tight">Logbook Management</h2>
            <p className="text-muted-foreground">
              Track your daily activities and weekly reports for your 10-week internship
            </p>
          </div>
        </div>

        <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
          <WeekSelector currentWeek={currentWeek} onWeekChange={changeWeek} />
          <div className="flex flex-col space-y-3 md:flex-row md:items-center md:space-y-0 md:space-x-3">
            {/* Week progress indicator */}
            <Card className="px-3 py-2">
              <CardContent className="p-0">
                <div className="flex items-center space-x-2 text-sm">
                  <span className="text-muted-foreground">Days logged:</span>
                  <span className="font-medium">{dailyLogs.length}/6</span>
                  {isWeekFull ? (
                    <CheckCircle2 className="h-4 w-4 text-green-500" />
                  ) : (
                    <AlertCircle className="h-4 w-4 text-orange-500" />
                  )}
                </div>
              </CardContent>
            </Card>

            <Button
              onClick={handleAddDailyLog}
              disabled={saving}
              variant={isWeekFull ? "outline" : "default"}
              className="w-full md:w-auto"
            >
              <PlusCircle className="mr-2 h-4 w-4" />
              <span className="md:hidden">
                {isWeekFull ? "Week Complete" : `Add ${nextDay ? availableDays.find(d => d.value === nextDay)?.label : "Daily Log"}`}
              </span>
              <span className="hidden md:inline">
                {isWeekFull ? "Week Complete" : `Add ${nextDay ? availableDays.find(d => d.value === nextDay)?.label : "Daily Log"}`}
              </span>
            </Button>
          </div>
        </div>

        <DottedSeparator />

        {loading ? (
          <div className="flex items-center justify-center h-40">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">Loading week {currentWeek} data...</p>
            </div>
          </div>
        ) : (
          <>
            {/* Daily logs section */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Daily Activities</h3>
                {dailyLogs.length === 0 && (
                  <p className="text-sm text-muted-foreground">
                    No daily logs yet. Click "Add {availableDays[0]?.label}" to get started.
                  </p>
                )}
              </div>

              {dailyLogs.length > 0 ? (
                <DataTable
                  data={dailyLogs}
                  columns={columns}
                  onEdit={handleEditDailyLog}
                />
              ) : (
                <Card className="p-8 text-center">
                  <CardContent className="p-0">
                    <div className="text-muted-foreground">
                      <PlusCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p className="text-lg font-medium mb-2">No daily logs for Week {currentWeek}</p>
                      <p className="text-sm">Start by adding your Monday activities</p>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>

            {/* Weekly report section */}
            <WeeklyReportEditor
              weekNumber={currentWeek}
              initialReport={weeklyReport || undefined}
              onSave={handleSaveReport}
              onSubmit={handleSubmitReport}
              disabled={saving}
            />
          </>
        )}
      </div>

      {/* Daily Log Form Dialog */}
      <DailyLogForm
        isOpen={showDailyLogForm}
        onClose={() => {
          setShowDailyLogForm(false)
          setEditingLog(null)
        }}
        onSave={handleSaveDailyLog}
        initialData={editingLog || undefined}
        weekNumber={currentWeek}
        availableDays={availableDays}
        nextDay={nextDay}
      />

      {/* Week Full Dialog */}
      <ConfirmationDialog
        isOpen={showWeekFullDialog}
        onClose={() => setShowWeekFullDialog(false)}
        onConfirm={() => setShowWeekFullDialog(false)}
        title="Week Complete"
        description="You have already logged all 6 days for this week (Monday through Saturday). You can edit existing entries by clicking on them in the table."
        confirmText="Got it"
        cancelText=""
      />
    </>
  )
}
