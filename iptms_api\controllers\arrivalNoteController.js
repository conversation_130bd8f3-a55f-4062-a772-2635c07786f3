const ArrivalNote = require('../models/ArrivalNote');

const checkup = async (req, res) => {
    const { user_id, ipt_id } = req.user;
    
        try {
            const result = await ArrivalNote.init(user_id, ipt_id);
    
            return res.status(200).json({
                success: true,
                data: result 
            });
    
        } catch (err) {
            console.error(err.message);
            return res.status(500).json({
                success: false,
                message: 'Internal server error.'
            });
        }
}
const allArrivalNotes = async (req, res) => {
    
        try {
            const result = await ArrivalNote.index();
    
            return res.status(200).json({
                success: true,
                data: result 
            });
    
        } catch (err) {
            console.error(err.message);
            return res.status(500).json({
                success: false,
                message: 'Internal server error.'
            });
        }
}

const singleArrivalNote = async (req, res) => {
    const id  = req.params.id;
    
        try {
            const result = await ArrivalNote.show(id);
    
            return res.status(200).json({
                success: true,
                data: result 
            });
            // console.log(result);
    
        } catch (err) {
            console.error(err.message);
            return res.status(500).json({
                success: false,
                message: 'Internal server error.'
            });
        }
}

const singleArrivalNoteByUserId = async (req, res) => {
    const id  = req.params.id;
    
        try {
            const result = await ArrivalNote.findByUserId(id);
    
            return res.status(200).json({
                success: true,
                data: result 
            });
            // console.log(result);
    
        } catch (err) {
            console.error(err.message);
            return res.status(500).json({
                success: false,
                message: 'Internal server error.'
            });
        }
}

const createArrivalNote = async (req, res) => {
    const  data  = req.body;
    // console.log(data);
    
        try {
            const result = await ArrivalNote.create(data);
    
            return res.status(200).json({
                success: true,
                message: 'Arrival note submitted successfully.',
                data: result 
            });
    
        } catch (err) {
            console.error(err.message);
            return res.status(500).json({
                success: false,
                message: 'Internal server error.'
            });
        }
}

const assignSupervisor = async (req, res) => {
    const student_id  = req.params.id;
    const { supervisor_id , arrivalNote_id } = req.body;
    
        try {
            const result = await ArrivalNote.assign(student_id, supervisor_id , arrivalNote_id);
    
            return res.status(200).json({
                success: true,
                message: 'Supervisor assigned successfully.',
                data: result 
            });
            // console.log(result);
    
        } catch (err) {
            console.error(err.message);
            return res.status(500).json({
                success: false,
                message: 'Internal server error.'
            });
        }
}

module.exports = {
    createArrivalNote,
    checkup,
    allArrivalNotes,
    singleArrivalNote,
    singleArrivalNoteByUserId,
    assignSupervisor
}