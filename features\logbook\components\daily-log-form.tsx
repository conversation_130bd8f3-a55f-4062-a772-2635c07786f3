"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>ontent, 
  <PERSON><PERSON><PERSON>eader, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select"
import { LogDay } from "../types/logbook"
import { statuses } from "../data/data"

interface DailyLogFormProps {
  isOpen: boolean
  onClose: () => void
  onSave: (data: LogDay) => void
  initialData?: Partial<LogDay>
  weekNumber: number
  availableDays?: { value: number; label: string; disabled?: boolean }[]
  nextDay?: number | null
}

type FormData = {
  title: string
  hours: string
  status: string
  day_number: number
}

const dayOptions = [
  { value: 1, label: "Monday" },
  { value: 2, label: "Tuesday" },
  { value: 3, label: "Wednesday" },
  { value: 4, label: "Thursday" },
  { value: 5, label: "Friday" },
  { value: 6, label: "Saturday" },
]

export function DailyLogForm({
  isOpen,
  onClose,
  onSave,
  initialData,
  weekNumber,
  availableDays = dayOptions,
  nextDay
}: DailyLogFormProps) {
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset
  } = useForm<FormData>({
    defaultValues: {
      title: initialData?.title || "",
      hours: initialData?.hours || "",
      status: initialData?.status || "todo",
      day_number: initialData?.day_number || nextDay || 1
    }
  });

  // Reset form when dialog opens/closes or when nextDay changes
  useEffect(() => {
    if (isOpen) {
      reset({
        title: initialData?.title || "",
        hours: initialData?.hours || "",
        status: initialData?.status || "todo",
        day_number: initialData?.day_number || nextDay || 1
      });
    }
  }, [isOpen, initialData, nextDay, reset]);

  const dayNumber = watch("day_number");

  const onSubmit = (data: FormData) => {
    const dayId = dayOptions.find(d => d.value === data.day_number)?.label.toUpperCase() || "DAY";
    
    const logData: LogDay = {
      id: initialData?.id || dayId,
      title: data.title,
      hours: data.hours,
      status: data.status,
      day_number: data.day_number,
      week_number: weekNumber
    };
    
    onSave(logData);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            {initialData?.id ? "Edit Daily Log" : "Add Daily Log"}
          </DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="day" className="text-right">
                Day
              </Label>
              <Select 
                value={dayNumber.toString()} 
                onValueChange={(value) => setValue("day_number", parseInt(value))}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select day" />
                </SelectTrigger>
                <SelectContent>
                  {availableDays.map((day) => (
                    <SelectItem
                      key={day.value}
                      value={day.value.toString()}
                      disabled={day.disabled}
                    >
                      <div className="flex items-center justify-between w-full">
                        <span>{day.label}</span>
                        {day.disabled && (
                          <span className="text-xs text-muted-foreground ml-2">
                            Already added
                          </span>
                        )}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="title" className="text-right">
                Activity
              </Label>
              <Input
                id="title"
                className="col-span-3"
                {...register("title", { required: "Activity is required" })}
              />
              {errors.title && (
                <p className="text-sm text-red-500 col-span-3 col-start-2">
                  {errors.title.message}
                </p>
              )}
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="hours" className="text-right">
                Hours
              </Label>
              <Input
                id="hours"
                className="col-span-3"
                {...register("hours", { 
                  required: "Hours are required",
                  pattern: {
                    value: /^[0-9]+$/,
                    message: "Please enter a valid number"
                  },
                  max: {
                    value: 24,
                    message: "Hours cannot exceed 24"
                  }
                })}
              />
              {errors.hours && (
                <p className="text-sm text-red-500 col-span-3 col-start-2">
                  {errors.hours.message}
                </p>
              )}
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="status" className="text-right">
                Status
              </Label>
              <Select 
                value={watch("status")} 
                onValueChange={(value) => setValue("status", value)}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  {statuses.map((status) => (
                    <SelectItem key={status.value} value={status.value}>
                      <div className="flex items-center">
                        <status.icon className="mr-2 h-4 w-4 text-muted-foreground" />
                        <span>{status.label}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit">
              {initialData?.id ? "Update" : "Create"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
