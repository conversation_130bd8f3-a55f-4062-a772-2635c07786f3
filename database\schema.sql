-- =====================================================
-- IPTMS Database Schema - SQL Server Version
-- Student Logbook and Reporting System
-- =====================================================

-- =====================================================
-- CORE TABLES
-- =====================================================

-- Users table (assuming this exists, but including for reference)
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='users' AND xtype='U')
CREATE TABLE users (
    id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    email NVARCHAR(255) UNIQUE NOT NULL,
    name NVARCHAR(255) NOT NULL,
    role NVARCHAR(50) NOT NULL CHECK (role IN ('student', 'supervisor', 'admin')),
    password_hash NVARCHAR(255) NOT NULL,
    is_active BIT DEFAULT 1,
    created_at DATETIME2 DEFAULT GETUTCDATE(),
    updated_at DATETIME2 DEFAULT GETUTCDATE()
);

-- Programs table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='programs' AND xtype='U')
CREATE TABLE programs (
    id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    name NVARCHAR(255) NOT NULL,
    description NTEXT,
    duration_weeks INT DEFAULT 10,
    is_active BIT DEFAULT 1,
    created_at DATETIME2 DEFAULT GETUTCDATE(),
    updated_at DATETIME2 DEFAULT GETUTCDATE()
);

-- Student profiles
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='student_profiles' AND xtype='U')
CREATE TABLE student_profiles (
    id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    user_id UNIQUEIDENTIFIER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    program_id UNIQUEIDENTIFIER REFERENCES programs(id),
    supervisor_id UNIQUEIDENTIFIER REFERENCES users(id),
    student_id NVARCHAR(50) UNIQUE,
    start_date DATE,
    end_date DATE,
    status NVARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'completed', 'suspended', 'withdrawn')),
    created_at DATETIME2 DEFAULT GETUTCDATE(),
    updated_at DATETIME2 DEFAULT GETUTCDATE()
);

-- =====================================================
-- LOGBOOK TABLES
-- =====================================================

-- Daily logs table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='daily_logs' AND xtype='U')
CREATE TABLE daily_logs (
    id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    student_id UNIQUEIDENTIFIER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    week_number INT NOT NULL CHECK (week_number >= 1 AND week_number <= 10),
    day_number INT NOT NULL CHECK (day_number >= 1 AND day_number <= 6),
    day_name NVARCHAR(20) NOT NULL CHECK (day_name IN ('Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday')),
    title NVARCHAR(255) NOT NULL,
    description NTEXT,
    hours NVARCHAR(10) NOT NULL,
    status NVARCHAR(50) DEFAULT 'todo' CHECK (status IN ('todo', 'in progress', 'done', 'canceled')),
    activities NTEXT, -- Store JSON activity data as text
    skills_learned NTEXT, -- Store as comma-separated or JSON
    challenges_faced NTEXT,
    supervisor_feedback NTEXT,
    created_at DATETIME2 DEFAULT GETUTCDATE(),
    updated_at DATETIME2 DEFAULT GETUTCDATE(),

    -- Ensure unique day per week per student
    UNIQUE(student_id, week_number, day_number),
    UNIQUE(student_id, week_number, day_name)
);

-- Weekly reports table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='weekly_reports' AND xtype='U')
CREATE TABLE weekly_reports (
    id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    student_id UNIQUEIDENTIFIER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    week_number INT NOT NULL CHECK (week_number >= 1 AND week_number <= 10),
    submission_type NVARCHAR(20) NOT NULL CHECK (submission_type IN ('write', 'upload')),

    -- Written report fields
    content NTEXT,
    character_count INT,

    -- File upload fields
    file_url NVARCHAR(500),
    file_name NVARCHAR(255),
    file_size BIGINT,
    file_type NVARCHAR(100),

    -- Status and metadata
    is_submitted BIT DEFAULT 0,
    submitted_at DATETIME2,
    supervisor_reviewed BIT DEFAULT 0,
    supervisor_feedback NTEXT,
    supervisor_rating INT CHECK (supervisor_rating >= 1 AND supervisor_rating <= 5),
    created_at DATETIME2 DEFAULT GETUTCDATE(),
    updated_at DATETIME2 DEFAULT GETUTCDATE(),

    -- Ensure one report per week per student
    UNIQUE(student_id, week_number)
);

-- =====================================================
-- FINAL REPORTS TABLE
-- =====================================================

IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='final_reports' AND xtype='U')
CREATE TABLE final_reports (
    id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    student_id UNIQUEIDENTIFIER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    submission_type NVARCHAR(20) NOT NULL CHECK (submission_type IN ('write', 'upload')),

    -- Written report fields
    content NTEXT,
    character_count INT,

    -- File upload fields
    file_url NVARCHAR(500),
    file_name NVARCHAR(255),
    file_size BIGINT,
    file_type NVARCHAR(100),

    -- Status and metadata
    is_submitted BIT DEFAULT 0,
    submitted_at DATETIME2,
    supervisor_reviewed BIT DEFAULT 0,
    supervisor_feedback NTEXT,
    supervisor_rating INT CHECK (supervisor_rating >= 1 AND supervisor_rating <= 5),
    admin_reviewed BIT DEFAULT 0,
    admin_feedback NTEXT,
    final_grade NVARCHAR(10),
    created_at DATETIME2 DEFAULT GETUTCDATE(),
    updated_at DATETIME2 DEFAULT GETUTCDATE(),

    -- Ensure one final report per student
    UNIQUE(student_id)
);

-- =====================================================
-- FEEDBACK TABLES
-- =====================================================

IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='feedback' AND xtype='U')
CREATE TABLE feedback (
    id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    student_id UNIQUEIDENTIFIER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    category NVARCHAR(50) NOT NULL CHECK (category IN ('general', 'program', 'supervision', 'facilities', 'recommendations')),
    title NVARCHAR(255) NOT NULL,
    submission_type NVARCHAR(20) NOT NULL CHECK (submission_type IN ('write', 'upload')),

    -- Written feedback fields
    content NTEXT,
    character_count INT,

    -- File upload fields
    file_url NVARCHAR(500),
    file_name NVARCHAR(255),
    file_size BIGINT,
    file_type NVARCHAR(100),

    -- Rating and status
    rating INT CHECK (rating >= 1 AND rating <= 5),
    is_submitted BIT DEFAULT 0,
    submitted_at DATETIME2,
    admin_reviewed BIT DEFAULT 0,
    admin_response NTEXT,
    created_at DATETIME2 DEFAULT GETUTCDATE(),
    updated_at DATETIME2 DEFAULT GETUTCDATE()
);

-- =====================================================
-- FILE UPLOADS TABLE (for better file management)
-- =====================================================

IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='file_uploads' AND xtype='U')
CREATE TABLE file_uploads (
    id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    user_id UNIQUEIDENTIFIER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    file_name NVARCHAR(255) NOT NULL,
    original_name NVARCHAR(255) NOT NULL,
    file_path NVARCHAR(500) NOT NULL,
    file_size BIGINT NOT NULL,
    file_type NVARCHAR(100) NOT NULL,
    mime_type NVARCHAR(100),
    upload_type NVARCHAR(50) NOT NULL CHECK (upload_type IN ('weekly_report', 'final_report', 'feedback', 'profile_document')),
    reference_id UNIQUEIDENTIFIER, -- References the related record (weekly_report.id, final_report.id, etc.)
    is_active BIT DEFAULT 1,
    created_at DATETIME2 DEFAULT GETUTCDATE(),
    updated_at DATETIME2 DEFAULT GETUTCDATE()
);

-- =====================================================
-- AUDIT TABLES
-- =====================================================

-- Activity logs for tracking user actions
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='activity_logs' AND xtype='U')
CREATE TABLE activity_logs (
    id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    user_id UNIQUEIDENTIFIER REFERENCES users(id) ON DELETE SET NULL,
    action NVARCHAR(100) NOT NULL,
    resource_type NVARCHAR(50) NOT NULL,
    resource_id UNIQUEIDENTIFIER,
    details NTEXT, -- Store JSON as text
    ip_address NVARCHAR(45), -- IPv6 compatible
    user_agent NTEXT,
    created_at DATETIME2 DEFAULT GETUTCDATE()
);

-- System notifications
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='notifications' AND xtype='U')
CREATE TABLE notifications (
    id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    user_id UNIQUEIDENTIFIER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    type NVARCHAR(50) NOT NULL,
    title NVARCHAR(255) NOT NULL,
    message NTEXT NOT NULL,
    data NTEXT, -- Store JSON as text
    is_read BIT DEFAULT 0,
    created_at DATETIME2 DEFAULT GETUTCDATE(),
    read_at DATETIME2
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Users indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_active ON users(is_active);

-- Student profiles indexes
CREATE INDEX idx_student_profiles_user_id ON student_profiles(user_id);
CREATE INDEX idx_student_profiles_program_id ON student_profiles(program_id);
CREATE INDEX idx_student_profiles_supervisor_id ON student_profiles(supervisor_id);
CREATE INDEX idx_student_profiles_student_id ON student_profiles(student_id);
CREATE INDEX idx_student_profiles_status ON student_profiles(status);

-- Daily logs indexes
CREATE INDEX idx_daily_logs_student_id ON daily_logs(student_id);
CREATE INDEX idx_daily_logs_week_number ON daily_logs(week_number);
CREATE INDEX idx_daily_logs_student_week ON daily_logs(student_id, week_number);
CREATE INDEX idx_daily_logs_status ON daily_logs(status);
CREATE INDEX idx_daily_logs_created_at ON daily_logs(created_at);

-- Weekly reports indexes
CREATE INDEX idx_weekly_reports_student_id ON weekly_reports(student_id);
CREATE INDEX idx_weekly_reports_week_number ON weekly_reports(week_number);
CREATE INDEX idx_weekly_reports_student_week ON weekly_reports(student_id, week_number);
CREATE INDEX idx_weekly_reports_submitted ON weekly_reports(is_submitted);
CREATE INDEX idx_weekly_reports_submission_type ON weekly_reports(submission_type);

-- Final reports indexes
CREATE INDEX idx_final_reports_student_id ON final_reports(student_id);
CREATE INDEX idx_final_reports_submitted ON final_reports(is_submitted);
CREATE INDEX idx_final_reports_submission_type ON final_reports(submission_type);
CREATE INDEX idx_final_reports_submitted_at ON final_reports(submitted_at);

-- Feedback indexes
CREATE INDEX idx_feedback_student_id ON feedback(student_id);
CREATE INDEX idx_feedback_category ON feedback(category);
CREATE INDEX idx_feedback_submitted ON feedback(is_submitted);
CREATE INDEX idx_feedback_rating ON feedback(rating);
CREATE INDEX idx_feedback_submission_type ON feedback(submission_type);

-- File uploads indexes
CREATE INDEX idx_file_uploads_user_id ON file_uploads(user_id);
CREATE INDEX idx_file_uploads_upload_type ON file_uploads(upload_type);
CREATE INDEX idx_file_uploads_reference_id ON file_uploads(reference_id);
CREATE INDEX idx_file_uploads_active ON file_uploads(is_active);

-- Activity logs indexes
CREATE INDEX idx_activity_logs_user_id ON activity_logs(user_id);
CREATE INDEX idx_activity_logs_action ON activity_logs(action);
CREATE INDEX idx_activity_logs_resource_type ON activity_logs(resource_type);
CREATE INDEX idx_activity_logs_created_at ON activity_logs(created_at);

-- Notifications indexes
CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_type ON notifications(type);
CREATE INDEX idx_notifications_read ON notifications(is_read);
CREATE INDEX idx_notifications_created_at ON notifications(created_at);

-- =====================================================
-- SAMPLE DATA (Optional - for testing)
-- =====================================================

-- Sample programs
INSERT INTO programs (name, description, duration_weeks) VALUES
('Computer Science Internship', 'Full-stack development internship program', 10),
('Data Science Internship', 'Data analysis and machine learning internship', 10),
('Cybersecurity Internship', 'Information security and ethical hacking program', 10);

-- =====================================================
-- STORED PROCEDURES (Optional)
-- =====================================================

-- Get student progress summary
CREATE PROCEDURE GetStudentProgress
    @StudentId UNIQUEIDENTIFIER
AS
BEGIN
    SELECT
        u.name as student_name,
        u.email,
        sp.student_id as student_number,
        p.name as program_name,
        COUNT(DISTINCT dl.id) as total_daily_logs,
        COUNT(DISTINCT dl.week_number) as weeks_with_logs,
        COUNT(DISTINCT CASE WHEN dl.status = 'done' THEN dl.id END) as completed_daily_logs,
        COUNT(DISTINCT wr.id) as total_weekly_reports,
        COUNT(DISTINCT CASE WHEN wr.is_submitted = 1 THEN wr.id END) as submitted_weekly_reports,
        CASE WHEN fr.is_submitted = 1 THEN 'Submitted' ELSE 'Not Submitted' END as final_report_status,
        COUNT(DISTINCT f.id) as total_feedback
    FROM users u
    LEFT JOIN student_profiles sp ON u.id = sp.user_id
    LEFT JOIN programs p ON sp.program_id = p.id
    LEFT JOIN daily_logs dl ON u.id = dl.student_id
    LEFT JOIN weekly_reports wr ON u.id = wr.student_id
    LEFT JOIN final_reports fr ON u.id = fr.student_id
    LEFT JOIN feedback f ON u.id = f.student_id
    WHERE u.id = @StudentId AND u.role = 'student'
    GROUP BY u.name, u.email, sp.student_id, p.name, fr.is_submitted;
END;

-- Get weekly summary for a student
CREATE PROCEDURE GetWeeklySummary
    @StudentId UNIQUEIDENTIFIER,
    @WeekNumber INT
AS
BEGIN
    SELECT
        dl.week_number,
        dl.day_number,
        dl.day_name,
        dl.title,
        dl.hours,
        dl.status,
        dl.created_at,
        wr.is_submitted as weekly_report_submitted,
        wr.submission_type as report_type
    FROM daily_logs dl
    LEFT JOIN weekly_reports wr ON dl.student_id = wr.student_id AND dl.week_number = wr.week_number
    WHERE dl.student_id = @StudentId AND dl.week_number = @WeekNumber
    ORDER BY dl.day_number;
END;

-- =====================================================
-- TRIGGERS AND FUNCTIONS
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers to all tables
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_programs_updated_at BEFORE UPDATE ON programs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_student_profiles_updated_at BEFORE UPDATE ON student_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_daily_logs_updated_at BEFORE UPDATE ON daily_logs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_weekly_reports_updated_at BEFORE UPDATE ON weekly_reports FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_final_reports_updated_at BEFORE UPDATE ON final_reports FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_feedback_updated_at BEFORE UPDATE ON feedback FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_file_uploads_updated_at BEFORE UPDATE ON file_uploads FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to automatically set submitted_at when is_submitted becomes true
CREATE OR REPLACE FUNCTION set_submitted_at()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.is_submitted = true AND OLD.is_submitted = false THEN
        NEW.submitted_at = CURRENT_TIMESTAMP;
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply submitted_at triggers
CREATE TRIGGER set_weekly_reports_submitted_at BEFORE UPDATE ON weekly_reports FOR EACH ROW EXECUTE FUNCTION set_submitted_at();
CREATE TRIGGER set_final_reports_submitted_at BEFORE UPDATE ON final_reports FOR EACH ROW EXECUTE FUNCTION set_submitted_at();
CREATE TRIGGER set_feedback_submitted_at BEFORE UPDATE ON feedback FOR EACH ROW EXECUTE FUNCTION set_submitted_at();

-- Function to validate day_name matches day_number
CREATE OR REPLACE FUNCTION validate_day_mapping()
RETURNS TRIGGER AS $$
BEGIN
    IF (NEW.day_number = 1 AND NEW.day_name != 'Monday') OR
       (NEW.day_number = 2 AND NEW.day_name != 'Tuesday') OR
       (NEW.day_number = 3 AND NEW.day_name != 'Wednesday') OR
       (NEW.day_number = 4 AND NEW.day_name != 'Thursday') OR
       (NEW.day_number = 5 AND NEW.day_name != 'Friday') OR
       (NEW.day_number = 6 AND NEW.day_name != 'Saturday') THEN
        RAISE EXCEPTION 'Day number % does not match day name %', NEW.day_number, NEW.day_name;
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply day validation trigger
CREATE TRIGGER validate_daily_logs_day_mapping BEFORE INSERT OR UPDATE ON daily_logs FOR EACH ROW EXECUTE FUNCTION validate_day_mapping();

-- Function to log activities
CREATE OR REPLACE FUNCTION log_activity()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO activity_logs (user_id, action, resource_type, resource_id, details)
    VALUES (
        COALESCE(NEW.student_id, NEW.user_id),
        TG_OP,
        TG_TABLE_NAME,
        COALESCE(NEW.id, OLD.id),
        jsonb_build_object(
            'old', to_jsonb(OLD),
            'new', to_jsonb(NEW)
        )
    );
    RETURN COALESCE(NEW, OLD);
END;
$$ language 'plpgsql';

-- Apply activity logging triggers (optional - can be resource intensive)
-- CREATE TRIGGER log_daily_logs_activity AFTER INSERT OR UPDATE OR DELETE ON daily_logs FOR EACH ROW EXECUTE FUNCTION log_activity();
-- CREATE TRIGGER log_weekly_reports_activity AFTER INSERT OR UPDATE OR DELETE ON weekly_reports FOR EACH ROW EXECUTE FUNCTION log_activity();
-- CREATE TRIGGER log_final_reports_activity AFTER INSERT OR UPDATE OR DELETE ON final_reports FOR EACH ROW EXECUTE FUNCTION log_activity();
-- CREATE TRIGGER log_feedback_activity AFTER INSERT OR UPDATE OR DELETE ON feedback FOR EACH ROW EXECUTE FUNCTION log_activity();

-- =====================================================
-- VIEWS FOR COMMON QUERIES
-- =====================================================

-- Student progress overview
CREATE OR REPLACE VIEW student_progress_overview AS
SELECT
    sp.user_id as student_id,
    u.name as student_name,
    u.email as student_email,
    sp.student_id as student_number,
    p.name as program_name,
    sup.name as supervisor_name,
    sp.start_date,
    sp.end_date,
    sp.status,

    -- Daily logs statistics
    COUNT(DISTINCT dl.id) as total_daily_logs,
    COUNT(DISTINCT dl.week_number) as weeks_with_logs,
    COUNT(DISTINCT CASE WHEN dl.status = 'done' THEN dl.id END) as completed_daily_logs,

    -- Weekly reports statistics
    COUNT(DISTINCT wr.id) as total_weekly_reports,
    COUNT(DISTINCT CASE WHEN wr.is_submitted THEN wr.id END) as submitted_weekly_reports,

    -- Final report status
    fr.is_submitted as final_report_submitted,
    fr.submitted_at as final_report_submitted_at,

    -- Feedback statistics
    COUNT(DISTINCT f.id) as total_feedback,
    COUNT(DISTINCT CASE WHEN f.is_submitted THEN f.id END) as submitted_feedback,

    -- Overall completion percentage
    ROUND(
        (COUNT(DISTINCT CASE WHEN dl.status = 'done' THEN dl.id END) * 1.0 / NULLIF(60, 0) * 40 +
         COUNT(DISTINCT CASE WHEN wr.is_submitted THEN wr.id END) * 1.0 / NULLIF(10, 0) * 40 +
         CASE WHEN fr.is_submitted THEN 20 ELSE 0 END) * 1.0, 2
    ) as overall_completion_percentage

FROM student_profiles sp
JOIN users u ON sp.user_id = u.id
LEFT JOIN programs p ON sp.program_id = p.id
LEFT JOIN users sup ON sp.supervisor_id = sup.id
LEFT JOIN daily_logs dl ON sp.user_id = dl.student_id
LEFT JOIN weekly_reports wr ON sp.user_id = wr.student_id
LEFT JOIN final_reports fr ON sp.user_id = fr.student_id
LEFT JOIN feedback f ON sp.user_id = f.student_id
WHERE u.role = 'student'
GROUP BY sp.user_id, u.name, u.email, sp.student_id, p.name, sup.name, sp.start_date, sp.end_date, sp.status, fr.is_submitted, fr.submitted_at;

-- Weekly summary view
CREATE OR REPLACE VIEW weekly_summary AS
SELECT
    dl.student_id,
    u.name as student_name,
    dl.week_number,
    COUNT(dl.id) as daily_logs_count,
    COUNT(CASE WHEN dl.status = 'done' THEN 1 END) as completed_logs,
    SUM(CAST(dl.hours AS INTEGER)) as total_hours,
    wr.is_submitted as weekly_report_submitted,
    wr.submission_type as report_submission_type,
    wr.submitted_at as report_submitted_at
FROM daily_logs dl
JOIN users u ON dl.student_id = u.id
LEFT JOIN weekly_reports wr ON dl.student_id = wr.student_id AND dl.week_number = wr.week_number
GROUP BY dl.student_id, u.name, dl.week_number, wr.is_submitted, wr.submission_type, wr.submitted_at
ORDER BY dl.student_id, dl.week_number;

-- Feedback summary by category
CREATE OR REPLACE VIEW feedback_summary AS
SELECT
    category,
    COUNT(*) as total_feedback,
    COUNT(CASE WHEN is_submitted THEN 1 END) as submitted_feedback,
    AVG(rating) as average_rating,
    COUNT(CASE WHEN submission_type = 'write' THEN 1 END) as written_feedback,
    COUNT(CASE WHEN submission_type = 'upload' THEN 1 END) as uploaded_feedback
FROM feedback
GROUP BY category
ORDER BY category;
