const db = require('../config/db');
const jwt = require('jsonwebtoken');
require('dotenv').config();

const register = async (userData) => {
    const { 
      email, 
      password, 
      fname, 
      mname, 
      lname, 
      regno, 
      studentClass, 
      program_id,
      ipt_id
     } = userData;

    try {
        // Start transaction
        await db.query('START TRANSACTION');

        const [existingUser] = await db.query('SELECT * FROM users WHERE email = ?', [email]);

        if (existingUser.length > 0) {
            await db.query('ROLLBACK');
            return { error: 'User already exists' };
        }

        // const hashedPassword = bcrypt.hashSync(password, 8);

        const [result] = await db.query(
            'INSERT INTO users (ipt_id , email, password, first_name, middle_name, last_name , created_at , updated_at) VALUES (?,?, ?, ?, ?, ? , NOW() , NOW())',
            [ipt_id , email, password, fname, mname, lname]
        );

        const userId = result.insertId;

        // insert into student  regno, class, program.
        await db.query(
            'INSERT INTO students (ipt_id , reg_no, class, program_id, user_id , created_at , updated_at) VALUES (?,?, ?, ?, ? , NOW() , NOW())',
            [ipt_id , regno, studentClass, program_id, userId]
        );

        const [roles] = await db.query('SELECT id FROM roles WHERE name = ?', ['student']);
        const roleId = roles[0].id;

        await db.query(
            'INSERT INTO user_has_role (user_id, role_id) VALUES (?, ?)',
            [userId, roleId]
        );

        // Commit transaction
        await db.query('COMMIT');

        return { success: true, userId: result.insertId };
    } catch (err) {
        // Rollback transaction on error
        await db.query('ROLLBACK');
        console.error(err.message);
        throw new Error('Error during registration process');
    }
}

const login = async (email, password) => {
    try {
        const [rows] = await db.query(`
            SELECT users.*, roles.name as role_name 
            FROM users 
            JOIN user_has_role ON users.id = user_has_role.user_id 
            JOIN roles ON user_has_role.role_id = roles.id 
            WHERE email = ?
        `, [email]);

        if (rows.length === 0) {
            return { error: 'User not found' };
        }

        const user = rows[0]; 
        const match = password == user.password;

        if (!match) {
            return { error: 'Invalid password' };
        }

        const token = jwt.sign({ 
            user_id: user.id,
            ipt_id: user.ipt_id,
            role: user.role_name
        }, process.env.JWT_SECRET || '1f6e9e76f7a08e8d7c27c69e7a6b9baf897ffba3b0e8f84b56b9dfce4c2e8e4b');

        return { success: true, token };
    } catch (err) {
        console.error(err.message);
        throw new Error('Error during login process');
    }
};

const getLoggedUser = async (userId) => {
    const [rows] = await db.query(
    `SELECT users.*, roles.name as role 
        FROM users 
        JOIN user_has_role ON users.id = user_has_role.user_id 
        JOIN roles ON user_has_role.role_id = roles.id 
        WHERE users.id = ?`, [
    userId,
    ]);
    return rows[0];
  };

const updateProfile = async (userId, userData) => {
  const { email, fname, lname, phone } = userData;
  
  try {
    // Check if email already exists for another user
    const emailCheck = await db.query(
      'SELECT * FROM users WHERE email = ? AND id != ?',
      [email, userId]
    );

    
    if (emailCheck[0].length > 0) {
      return { error: 'Email already in use by another account' };
    }
    
    // Update user profile
    await db.query(
      `UPDATE users 
       SET email = ?, first_name = ?, last_name = ?, phone = ?, updated_at = NOW()
       WHERE id = ?`,
      [email, fname, lname, phone, userId]
    );

    const user = await getLoggedUser(userId);
    
    return { success: true , data: user };
  } catch (err) {
    console.error(err.message);
    throw new Error('Error updating user profile');
  }
};

module.exports = {
  login,
  getLoggedUser,
  register,
  updateProfile
};
