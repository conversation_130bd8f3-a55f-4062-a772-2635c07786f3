const db = require('../config/db');
const { sendSupervisorInvite } = require('../lib/emailService');
const bcrypt = require('bcryptjs');
// const crypto = require('crypto');

const jwt = require('jsonwebtoken');
require('dotenv').config();

const ROLE_NAME = "supervisor";

const init = async (user_id,ipt_id) => {
    try {
        const [rows] = await db.query('SELECT needsPasswordChange FROM users WHERE id = ? AND ipt_id = ?', [user_id, ipt_id]);
      
        return rows[0].needsPasswordChange === 1;
    } catch (err) {
        console.error(err.message);
        throw new Error('Internal server error.');
    }
}

const index = async () => {
    try {
        const [rows] = await db.query(`
            SELECT u.* 
            FROM users u
            INNER JOIN user_has_role uhr on uhr.user_id = u.id
            INNER JOIN roles r on r.id = uhr.role_id
            WHERE r.name = ?;
        `,[ROLE_NAME]);
        return rows;
    } catch (err) {
        console.error(err.message);
        throw new Error('Internal server error.');
    }
}

const show = async (supervisor_id) => {
    try {
        const [rows] = await db.query(`
            SELECT u.* 
            FROM users u
            INNER JOIN user_has_role uhr on uhr.user_id = u.id
            INNER JOIN roles r on r.id = uhr.role_id
            WHERE r.name = ? AND u.id = ?;
        `,[ROLE_NAME , supervisor_id]);
        return rows;
    } catch (err) {
        console.error(err.message);
        throw new Error('Internal server error.');
    }
}

const create = async (first_name, last_name, email, phone, user_id , ipt_id) => {
    try {
        const [existingSupervisor] = await db.query(`
            SELECT u.* 
            FROM users u
            INNER JOIN user_has_role uhr on uhr.user_id = u.id
            INNER JOIN roles r on r.id = uhr.role_id
            WHERE r.name = ? AND u.email = ? AND u.ipt_id = ?;
        `, [ROLE_NAME, email, ipt_id]);

        if (existingSupervisor.length > 0) {
            throw new Error('Supervisor with this email already exists.');
        }

        // const otp = crypto.randomBytes(4).toString("hex"); 
        const otp = Math.floor(100000 + Math.random() * 900000).toString();
        // const hashedOtp = await bcrypt.hash(otp, 10); 

        console.log('OTP:', otp); 

        // Insert into users table
        const [userResult] = await db.query(`
            INSERT INTO users (ipt_id,first_name, last_name, email, phone,password,needsPasswordChange,created_by,created_at,updated_at)
            VALUES (?,?, ?, ?, ?, ?,1,?,NOW(),NOW());
        `, [ipt_id,first_name, last_name, email, phone,otp,user_id]);

        const newUserId = userResult.insertId;

        // Get role_id for supervisor
        const [role] = await db.query(`
            SELECT id 
            FROM roles 
            WHERE name = ?;
        `, [ROLE_NAME]);

        if (role.length === 0) {
            throw new Error('Supervisor role not found.');
        }

        const roleId = role[0].id;

        // Insert into user_has_role table
        await db.query(`
            INSERT INTO user_has_role (user_id, role_id)
            VALUES (?, ?);
        `, [newUserId, roleId]);
            
        const loginLink = 'http://localhost:3000/?tag=otp&email=' + email;
        console.log(loginLink)
        await sendSupervisorInvite(email, otp, `${first_name} ${last_name}`, loginLink);
          
        return true;
    } catch (err) {
        throw new Error(err.message);
        console.log(err.message);
    }
}
const update = async (first_name, last_name, email, phone , ipt_id , supervisor_id) => {
    try {
        const [existingSupervisor] = await db.query(`
            SELECT u.* 
            FROM users u
            INNER JOIN user_has_role uhr on uhr.user_id = u.id
            INNER JOIN roles r on r.id = uhr.role_id
            WHERE r.name = ? AND u.email = ? AND u.ipt_id = ?;
        `, [ROLE_NAME, email, ipt_id]);

        if (existingSupervisor.length < 1) {
            throw new Error('Supervisor with this email Not found.');
        }
 

        // Update users table
        await db.query(`
            UPDATE users 
            SET first_name = ?, last_name = ?, email = ?, phone = ? 
            WHERE id = ? AND ipt_id = ?;
        `, [first_name, last_name, email, phone, supervisor_id, ipt_id]);

       return true;
    } catch (err) {
        throw new Error(err.message);
    }
}

const destroy = async (supervisor_id) => {
    try {
        const [existingSupervisor] = await db.query(`
            SELECT u.id 
            FROM users u
            INNER JOIN user_has_role uhr on uhr.user_id = u.id
            INNER JOIN roles r on r.id = uhr.role_id
            WHERE r.name = ? AND u.id = ?;
        `, [ROLE_NAME, supervisor_id]);

        if (existingSupervisor.length === 0) {
            throw new Error('Supervisor not found.');
        }

        await db.query(`
            DELETE FROM user_has_role
            WHERE user_id = ?;
        `, [supervisor_id]);

        await db.query(`
            DELETE FROM users
            WHERE id = ?;
        `, [supervisor_id]);

        return true;
    } catch (err) {
        console.error(err.message);
        throw new Error(err.message);
    }
};

const login = async (email , inputOtp) => {
    try {
        const [rows] = await db.query(`
            SELECT users.*, roles.name as role_name 
            FROM users 
            JOIN user_has_role ON users.id = user_has_role.user_id 
            JOIN roles ON user_has_role.role_id = roles.id 
            WHERE email = ?;
        `, [email]);

        if (rows.length === 0) {
            throw new Error('User not found');
        }

        const user = rows[0];
        // const isMatch = await bcrypt.compare(inputOtp, user.password);

        if (inputOtp !== user.password) {
            throw new Error('Invalid OTP');
        }

        await db.query(`
            UPDATE users SET needsPasswordChange = 1 WHERE email = ?;
        `, [email]);

        const token = jwt.sign({ 
            user_id: user.id,
            ipt_id: user.ipt_id,
            role: user.role_name
        }, process.env.JWT_SECRET || '1f6e9e76f7a08e8d7c27c69e7a6b9baf897ffba3b0e8f84b56b9dfce4c2e8e4b');
        
        return { success: true, token };

    } catch (err) {
        throw new Error(err.message);
    }
}

const changePassword  = async (user_id,ipt_id, newPassword) => {
    try {
        await db.query(`
            UPDATE users 
            SET password = ?, needsPasswordChange = 0 
            WHERE id = ? AND ipt_id = ?;
        `, [newPassword, user_id, ipt_id]);

    } catch (err) {
        throw new Error(err.message);
    }
}


module.exports = {
    init,
    index,
    show,
    create,
    update,
    destroy,
    login,
    changePassword
}