const Program = require('../models/Program');

const allPrograms = async (req, res) => {
    try {
        const programs = await Program.index();

        return res.status(200).json({
            success: true,
            message: 'programs fetched successfully.',
            data: programs 
        });

    } catch (err) {
        console.error(err.message);
        return res.status(500).json({
            success: false,
            message: 'Internal server error.'
        });
    }
};

module.exports = { allPrograms };