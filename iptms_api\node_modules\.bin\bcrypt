#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/03EM14/iptms/iptms_api/node_modules/.pnpm/bcryptjs@3.0.2/node_modules/bcryptjs/bin/node_modules:/mnt/d/03EM14/iptms/iptms_api/node_modules/.pnpm/bcryptjs@3.0.2/node_modules/bcryptjs/node_modules:/mnt/d/03EM14/iptms/iptms_api/node_modules/.pnpm/bcryptjs@3.0.2/node_modules:/mnt/d/03EM14/iptms/iptms_api/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/03EM14/iptms/iptms_api/node_modules/.pnpm/bcryptjs@3.0.2/node_modules/bcryptjs/bin/node_modules:/mnt/d/03EM14/iptms/iptms_api/node_modules/.pnpm/bcryptjs@3.0.2/node_modules/bcryptjs/node_modules:/mnt/d/03EM14/iptms/iptms_api/node_modules/.pnpm/bcryptjs@3.0.2/node_modules:/mnt/d/03EM14/iptms/iptms_api/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../bcryptjs/bin/bcrypt" "$@"
else
  exec node  "$basedir/../bcryptjs/bin/bcrypt" "$@"
fi
