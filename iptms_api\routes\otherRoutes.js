const express = require('express');
const { createIPT, allIPTs } = require('../controllers/iptController');
const { allPrograms } = require('../controllers/programController');
const { createArrivalNote, checkup, allArrivalNotes, singleArrivalNote, singleArrivalNoteByUserId, assignSupervisor } = require('../controllers/arrivalNoteController');
const { verifyJWT } = require('../middlewares/authMiddleware');
const { getAllStudents, getSingleStudent } = require('../controllers/studentController');
const { getAllSupervisors, getSingleSupervisor, createSupervisor, deleteSupervisor, supervisorLogin, supervisorCheckup, changePassword, editSupervisor } = require('../controllers/supervisorController');
const router = express.Router();

// IPT routes
router.get('/getIPTs', allIPTs);
router.post('/newIPT', createIPT);

// Programs routes
router.get('/getPrograms', allPrograms);

// arrival note routes
router.get('/getArrivalNotes', allArrivalNotes);
router.get('/getArrivalNote/:id', singleArrivalNote);
router.get('/getArrivalNoteByUserId/:id', singleArrivalNoteByUserId);
router.post('/newArrivalNote', createArrivalNote);
router.post('/arrivalNote/assign/:id',assignSupervisor)
router.get('/checkup', verifyJWT , checkup);

// students routes
router.get('/getStudents',getAllStudents)
router.get('/getStudent/:id',getSingleStudent)

// supervisors routes
router.get('/supervisors/checkup',verifyJWT,supervisorCheckup)
router.get('/supervisors',getAllSupervisors)
router.post('/supervisors' , verifyJWT , createSupervisor)
router.put('/supervisors/:id',editSupervisor )
router.get('/supervisors/:id', getSingleSupervisor)
router.delete('/supervisors/:id' ,deleteSupervisor)
router.post('/supervisors/login', supervisorLogin)
router.post('/supervisors/changePassword',verifyJWT, changePassword)

module.exports = router;
