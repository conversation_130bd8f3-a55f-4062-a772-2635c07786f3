const express = require('express');
const { createIPT, allIPTs } = require('../controllers/iptController');
const { allPrograms } = require('../controllers/programController');
const { createArrivalNote, checkup, allArrivalNotes, singleArrivalNote, singleArrivalNoteByUserId, assignSupervisor } = require('../controllers/arrivalNoteController');
const { verifyJWT } = require('../middlewares/authMiddleware');
const { getAllStudents, getSingleStudent } = require('../controllers/studentController');
const { getAllSupervisors, getSingleSupervisor, createSupervisor, deleteSupervisor, supervisorLogin, supervisorCheckup, changePassword, editSupervisor } = require('../controllers/supervisorController');
const {
    getAllFeedback,
    getSingleFeedback,
    getStudentFeedback,
    getFeedbackByType,
    createFeedback,
    updateFeedback,
    deleteFeedback,
    getFeedbackForReports,
    saveFeedbackForReports,
    updateFeedbackStatus
} = require('../controllers/feedbackController');
const {
    getAllRecommendations,
    getSingleRecommendation,
    getStudentRecommendations,
    getSupervisorRecommendations,
    createRecommendation,
    updateRecommendation,
    deleteRecommendation,
    markRecommendationAsRead,
    updateRecommendationStatus,
    respondToRecommendation,
    getRecommendationsWithFilter
} = require('../controllers/recommendationController');
const router = express.Router();

// IPT routes
router.get('/getIPTs', allIPTs);
router.post('/newIPT', createIPT);

// Programs routes
router.get('/getPrograms', allPrograms);

// arrival note routes
router.get('/getArrivalNotes', allArrivalNotes);
router.get('/getArrivalNote/:id', singleArrivalNote);
router.get('/getArrivalNoteByUserId/:id', singleArrivalNoteByUserId);
router.post('/newArrivalNote', createArrivalNote);
router.post('/arrivalNote/assign/:id',assignSupervisor)
router.get('/checkup', verifyJWT , checkup);

// students routes
router.get('/getStudents',getAllStudents)
router.get('/getStudent/:id',getSingleStudent)

// supervisors routes
router.get('/supervisors/checkup',verifyJWT,supervisorCheckup)
router.get('/supervisors',getAllSupervisors)
router.post('/supervisors' , verifyJWT , createSupervisor)
router.put('/supervisors/:id',editSupervisor )
router.get('/supervisors/:id', getSingleSupervisor)
router.delete('/supervisors/:id' ,deleteSupervisor)
router.post('/supervisors/login', supervisorLogin)
router.post('/supervisors/changePassword',verifyJWT, changePassword)

// Feedback routes - Standard CRUD
router.get('/getFeedback', getAllFeedback);
router.get('/getFeedback/:id', getSingleFeedback);
router.get('/getFeedbackByStudent/:student_id', getStudentFeedback);
router.get('/getFeedbackByType/:type', getFeedbackByType);
router.post('/newFeedback', createFeedback);
router.put('/updateFeedback/:id', updateFeedback);
router.delete('/deleteFeedback/:id', deleteFeedback);
router.patch('/updateFeedbackStatus/:id', updateFeedbackStatus);

// Recommendation routes - Standard CRUD
router.get('/getRecommendations', getAllRecommendations);
router.get('/getRecommendation/:id', getSingleRecommendation);
router.get('/getRecommendationsByStudent/:student_id', getStudentRecommendations);
router.get('/getRecommendationsBySupervisor/:supervisor_id', getSupervisorRecommendations);
router.post('/newRecommendation', createRecommendation);
router.put('/updateRecommendation/:id', updateRecommendation);
router.delete('/deleteRecommendation/:id', deleteRecommendation);
router.patch('/markRecommendationAsRead/:id', markRecommendationAsRead);
router.patch('/updateRecommendationStatus/:id', updateRecommendationStatus);
router.post('/respondToRecommendation/:id', respondToRecommendation);
router.get('/getRecommendationsWithFilter', verifyJWT, getRecommendationsWithFilter);

module.exports = router;
