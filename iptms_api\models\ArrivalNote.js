const db = require('../config/db');

const init = async (user_id,ipt_id) => {
    // console.log(user_id,ipt_id);
    try {
        const [rows] = await db.query('SELECT COUNT(*) as count FROM arrival_notes WHERE user_id = ? AND ipt_id = ?', [user_id, ipt_id]);
      
        return rows[0].count > 0;
    } catch (err) {
        console.error(err.message);
        throw new Error('Internal server error.');
    }
}

const index = async () => {
    try {
        const [rows] = await db.query(`
            SELECT an.id , an.is_assigned , u.first_name , u.middle_name , u.last_name , s.id as student_id,s.reg_no , s.class ,an.created_at , an.updated_at
            FROM arrival_notes an
            INNER JOIN users u on u.id = an.user_id
            INNER JOIN students s on s.user_id = u.id;
        `);
        return rows;
    } catch (err) {
        console.error(err.message);
        throw new Error('Internal server error.');
    }
}

const show = async (id) => {
    try {
        const [rows] = await db.query(`
            SELECT an.*, u.first_name , u.middle_name , u.last_name , s.reg_no , s.class
            FROM arrival_notes an
            INNER JOIN users u on u.id = an.user_id
            INNER JOIN students s on s.user_id = u.id 
            WHERE an.id = ?;
        `, [id]);
        return rows;
    } catch (err) {
        console.error(err.message);
        throw new Error('Internal server error.');
    }
}

const findByUserId = async (id) => {
    try {
        const [rows] = await db.query(`
            SELECT 
                an.*, 
                u.first_name, 
                u.middle_name, 
                u.last_name, 
                s.reg_no, 
                s.class AS student_class
            FROM arrival_notes an
            INNER JOIN users u ON u.id = an.user_id
            LEFT JOIN students s ON s.user_id = u.id
            WHERE an.user_id = ?;
        `, [id]);

        return rows;
    } catch (err) {
        console.error(err.message);
        throw new Error('Internal server error.');
    }
}

const create = async (data) => {
    const {
        user_id,
        ipt_id,
        student_phone,
        organization,
        i_supervisor_name,
        i_supervisor_phone,
        region,
        district,
        ward,
        street,
        house_number
    } = data;

    try {
        const result = await db.query(`
        INSERT INTO arrival_notes (
            user_id,
            ipt_id,
            organization,
            i_supervisor_name,
            i_supervisor_phone,
            region,
            district,
            ward,
            street,
            house_number,
            created_at,
            updated_at
        ) VALUES (?,?,?,?,?,?,?,?,?,?,NOW(),NOW())
        `
        , [
            user_id,
            ipt_id,
            organization,
            i_supervisor_name,
            i_supervisor_phone,
            region,
            district,
            ward,
            street,
            house_number
        ]);

        const arrivalNoteId = result.insertId;

        await db.query(`
         UPDATE users SET phone = ? WHERE id = ?
        `,[
            student_phone,
            user_id
        ]);

        return result;
    }catch (err) {
        console.error(err.message);
        throw new Error('Internal server error.');
    }
}

const assign = async (student_id, supervisor_id , arrivalNote_id) => {
    try {
        const result = await db.query(`
            UPDATE students SET supervisor_id = ? WHERE id = ?
        `, [supervisor_id, student_id]);
        
        await db.query(`
            UPDATE arrival_notes SET is_assigned = ? WHERE id = ?
        `, [supervisor_id, arrivalNote_id]);

        return !!result;
    } catch (err) {
        console.error(err.message);
        throw new Error('Internal server error.');
    }
}

module.exports = {
    init,
    index,
    show,
    findByUserId,
    create,
    assign
}