const db = require('../config/db');

const index = async () => {
    try {
        const [rows] = await db.query(`
            SELECT 
                f.id, 
                f.category, 
                f.title, 
                f.submission_type,
                f.content,
                f.character_count,
                f.file_url,
                f.file_name,
                f.file_size,
                f.file_type,
                f.rating,
                f.is_submitted,
                f.submitted_at,
                f.admin_reviewed,
                f.admin_response,
                f.created_at,
                f.updated_at,
                u.first_name, 
                u.middle_name, 
                u.last_name,
                u.email,
                s.reg_no, 
                s.class
            FROM feedback f
            INNER JOIN users u ON u.id = f.student_id
            INNER JOIN students s ON s.user_id = u.id
            ORDER BY f.created_at DESC;
        `);
        return rows;
    } catch (err) {
        console.error(err.message);
        throw new Error('Internal server error.');
    }
}

const show = async (id) => {
    try {
        const [rows] = await db.query(`
            SELECT 
                f.*,
                u.first_name, 
                u.middle_name, 
                u.last_name,
                u.email,
                s.reg_no, 
                s.class
            FROM feedback f
            INNER JOIN users u ON u.id = f.student_id
            INNER JOIN students s ON s.user_id = u.id 
            WHERE f.id = ?;
        `, [id]);
        return rows;
    } catch (err) {
        console.error(err.message);
        throw new Error('Internal server error.');
    }
}

const getByStudent = async (student_id) => {
    try {
        const [rows] = await db.query(`
            SELECT 
                f.*,
                u.first_name, 
                u.middle_name, 
                u.last_name,
                u.email,
                s.reg_no, 
                s.class
            FROM feedback f
            INNER JOIN users u ON u.id = f.student_id
            INNER JOIN students s ON s.user_id = u.id
            WHERE f.student_id = ?
            ORDER BY f.created_at DESC;
        `, [student_id]);

        return rows;
    } catch (err) {
        console.error(err.message);
        throw new Error('Internal server error.');
    }
}

const getByType = async (feedback_type) => {
    try {
        const [rows] = await db.query(`
            SELECT 
                f.*,
                u.first_name, 
                u.middle_name, 
                u.last_name,
                u.email,
                s.reg_no, 
                s.class
            FROM feedback f
            INNER JOIN users u ON u.id = f.student_id
            INNER JOIN students s ON s.user_id = u.id
            WHERE f.category = ?
            ORDER BY f.created_at DESC;
        `, [feedback_type]);

        return rows;
    } catch (err) {
        console.error(err.message);
        throw new Error('Internal server error.');
    }
}

const create = async (data) => {
    const {
        student_id,
        category,
        title,
        submission_type,
        content,
        character_count,
        file_url,
        file_name,
        file_size,
        file_type,
        rating,
        is_submitted
    } = data;

    try {
        const [result] = await db.query(`
        INSERT INTO feedback (
            student_id,
            category,
            title,
            submission_type,
            content,
            character_count,
            file_url,
            file_name,
            file_size,
            file_type,
            rating,
            is_submitted,
            submitted_at,
            created_at,
            updated_at
        ) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,NOW(),NOW())
        `, [
            student_id,
            category,
            title,
            submission_type,
            content || null,
            character_count || null,
            file_url || null,
            file_name || null,
            file_size || null,
            file_type || null,
            rating || null,
            is_submitted || 0,
            is_submitted ? new Date() : null
        ]);

        // Get the created feedback with user details
        const insertId = result.insertId || result[0]?.insertId;
        if (!insertId) {
            throw new Error('Failed to get inserted feedback ID');
        }

        const [createdFeedback] = await db.query(`
            SELECT
                f.*,
                u.first_name,
                u.middle_name,
                u.last_name,
                u.email,
                s.reg_no,
                s.class
            FROM feedback f
            INNER JOIN users u ON u.id = f.student_id
            INNER JOIN students s ON s.user_id = u.id
            WHERE f.id = ?;
        `, [insertId]);

        return createdFeedback[0];
    } catch (err) {
        console.error(err.message);
        throw new Error('Internal server error.');
    }
}

const update = async (id, data) => {
    const {
        category,
        title,
        submission_type,
        content,
        character_count,
        file_url,
        file_name,
        file_size,
        file_type,
        rating,
        is_submitted
    } = data;

    try {
        const [result] = await db.query(`
            UPDATE feedback SET 
                category = ?,
                title = ?,
                submission_type = ?,
                content = ?,
                character_count = ?,
                file_url = ?,
                file_name = ?,
                file_size = ?,
                file_type = ?,
                rating = ?,
                is_submitted = ?,
                submitted_at = ?,
                updated_at = NOW()
            WHERE id = ?
        `, [
            category,
            title,
            submission_type,
            content || null,
            character_count || null,
            file_url || null,
            file_name || null,
            file_size || null,
            file_type || null,
            rating || null,
            is_submitted || 0,
            is_submitted ? new Date() : null,
            id
        ]);

        // Get the updated feedback with user details
        const [updatedFeedback] = await db.query(`
            SELECT 
                f.*,
                u.first_name, 
                u.middle_name, 
                u.last_name,
                u.email,
                s.reg_no, 
                s.class
            FROM feedback f
            INNER JOIN users u ON u.id = f.student_id
            INNER JOIN students s ON s.user_id = u.id
            WHERE f.id = ?;
        `, [id]);

        return updatedFeedback[0];
    } catch (err) {
        console.error(err.message);
        throw new Error('Internal server error.');
    }
}

const deleteFeedback = async (id) => {
    try {
        const [result] = await db.query(`
            DELETE FROM feedback WHERE id = ?
        `, [id]);

        return !!result.affectedRows;
    } catch (err) {
        console.error(err.message);
        throw new Error('Internal server error.');
    }
}

module.exports = {
    index,
    show,
    getByStudent,
    getByType,
    create,
    update,
    delete: deleteFeedback
}
