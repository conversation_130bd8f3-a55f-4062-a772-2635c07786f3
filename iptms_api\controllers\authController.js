const Auth = require('../models/Auth');

const login = async (req, res) => {
  const { email, password } = req.body;
    
  try {
    const result = await Auth.login(email, password);

    if (result.error) {
      return res.status(400).json({
        success: false,
        message: result.error
      });
    }

    return res.status(200).json({
      success: true,
      message: 'Login successful.',
      token: result.token
    });
  } catch (err) {
    console.error(err.message);
    return res.status(500).json({
      success: false,
      message: 'Internal server error.'
    });
  }
};

const register = async (req, res) => {
  const { email, password, fname, mname, lname, regno, class: studentClass, program_id, ipt_id } = req.body;

  try {
    const result = await Auth.register({
      email,
      password,
      fname,
      mname,
      lname,
      regno,
      studentClass,
      program_id,
      ipt_id
    });

    if (result.error) {
      return res.status(400).json({
        success: false,
        message: result.error
      });
    }

    return res.status(200).json({
      success: true,
      message: 'Registration successful.',
      userId: result.userId
    });
  } catch (err) {
    console.error(err.message);
    return res.status(500).json({
      success: false,
      message: 'Internal server error.'
    });
  }
};

const getLoggedUser = async (req, res) => {
  try {
    const { user_id } = req.user;
  
    const user = await Auth.getLoggedUser(user_id);
  
    if (!user) {
      return res.status(404).json({
        success: false,
        message: "Logged User not found",
      });
    }

      return res.status(200).json({
        success: true,
        message: "Welcome Again",
        data: user,
      });

    } catch (error) {
      return res.status(500).json({
        success: false,
        error: error,
      });
    }
};

const updateProfile = async (req, res) => {
  try {
    const { user_id } = req.user;
    const { email, fname, lname, phone } = req.body;
    
    const result = await Auth.updateProfile(user_id, {
      email,
      fname,
      lname,
      phone
    });
    
    return res.status(200).json({
      success: true,
      message: "Profile updated successfully",
      data: result.data
    });
    
  } catch (error) {
    console.error(error.message);
    return res.status(500).json({
      success: false,
      message: error.message
    });
  }
};

module.exports = {
    login,
    getLoggedUser,
    register,
    updateProfile
};
