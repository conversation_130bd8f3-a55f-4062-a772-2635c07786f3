const express = require('express');
const { login, getLoggedUser, register, updateProfile } = require('../controllers/authController');
const { verifyJWT } = require('../middlewares/authMiddleware');

const router = express.Router();


router.post('/register', register);
router.post('/login', login);
router.get('/getLoggedInUser', verifyJWT, getLoggedUser);
router.patch('/updateProfile', verifyJWT, updateProfile);

module.exports = router;
