export interface LogDay {
  id: string;
  title: string;
  status: string;
  hours: string;
  day_number: number;
  week_number: number;
  student_id?: string;
  created_at?: string;
  updated_at?: string;
}

export interface WeeklyReport {
  id?: string;
  content?: string;
  week_number: number;
  student_id?: string;
  submission_type: 'write' | 'upload';
  file_url?: string;
  file_name?: string;
  file_size?: number;
  file_type?: string;
  character_count?: number;
  is_submitted?: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface LogbookWeek {
  week_number: number;
  days: LogDay[];
  report: WeeklyReport;
}

export interface FileUpload {
  file: File;
  preview?: string;
  progress?: number;
  error?: string;
}

export interface DayOption {
  value: number;
  label: string;
  disabled?: boolean;
}