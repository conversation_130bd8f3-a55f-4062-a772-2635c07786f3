'use client'

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { 
  Download, 
  Search, 
  Filter,
  BookOpen,
  Calendar,
  User,
  Clock,
  CheckCircle2,
  AlertCircle,
  Eye
} from "lucide-react"
import { Reports } from "@/features/reports/server/Reports"
import { AdminLogbookView, AdminReportFilters } from "@/features/reports/types/reports"
import { toast } from "sonner"

export default function AdminLogbooksPage() {
  const [loading, setLoading] = useState(true)
  const [logbooks, setLogbooks] = useState<AdminLogbookView[]>([])
  const [filteredLogbooks, setFilteredLogbooks] = useState<AdminLogbookView[]>([])
  const [filters, setFilters] = useState<AdminReportFilters>({
    student_name: '',
    status: 'all',
    program: '',
    supervisor: ''
  })
  const [expandedStudent, setExpandedStudent] = useState<string | null>(null)

  useEffect(() => {
    loadLogbooks()
  }, [])

  useEffect(() => {
    applyFilters()
  }, [logbooks, filters])

  const loadLogbooks = async () => {
    setLoading(true)
    try {
      const result = await Reports.getStudentLogbooks()
      const data = result?.data || []
      setLogbooks(data)
    } catch (error) {
      console.error("Failed to load logbooks:", error)
      setLogbooks([])
    } finally {
      setLoading(false)
    }
  }

  const applyFilters = () => {
    let filtered = [...logbooks]

    if (filters.student_name) {
      filtered = filtered.filter(logbook => 
        logbook.student_name.toLowerCase().includes(filters.student_name!.toLowerCase())
      )
    }

    if (filters.status && filters.status !== 'all') {
      filtered = filtered.filter(logbook => {
        const weeklyReportStatus = logbook.weekly_report.is_submitted ? 'completed' : 'pending'
        const dailyLogsStatus = logbook.daily_logs.length >= 6 ? 'completed' : 'pending'
        
        switch (filters.status) {
          case 'completed':
            return weeklyReportStatus === 'completed' && dailyLogsStatus === 'completed'
          case 'pending':
            return weeklyReportStatus === 'pending' || dailyLogsStatus === 'pending'
          default:
            return true
        }
      })
    }

    setFilteredLogbooks(filtered)
  }

  const handleExport = async () => {
    try {
      await Reports.exportReports('logbooks', 'excel', filters)
    } catch (error) {
      toast.error("Failed to export logbooks")
    }
  }

  const getWeekStatus = (logbook: AdminLogbookView) => {
    const dailyLogsComplete = logbook.daily_logs.length >= 6
    const weeklyReportComplete = logbook.weekly_report.is_submitted
    
    if (dailyLogsComplete && weeklyReportComplete) {
      return { status: 'completed', color: 'bg-green-100 text-green-800' }
    } else if (logbook.daily_logs.length > 0 || logbook.weekly_report.content) {
      return { status: 'in-progress', color: 'bg-yellow-100 text-yellow-800' }
    } else {
      return { status: 'not-started', color: 'bg-gray-100 text-gray-800' }
    }
  }

  const toggleStudentExpansion = (studentId: string) => {
    setExpandedStudent(expandedStudent === studentId ? null : studentId)
  }

  if (loading) {
    return (
      <div className="h-full flex-1 flex-col space-y-4 p-4 md:space-y-8 md:p-8 flex">
        <div className="flex items-center justify-center h-40">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading student logbooks...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex-1 flex-col space-y-4 p-4 md:space-y-8 md:p-8 flex">
      <div className="flex items-center justify-between space-y-2">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Student Logbooks</h2>
          <p className="text-muted-foreground">
            Review and manage all student logbook submissions
          </p>
        </div>
        
        <Button onClick={handleExport}>
          <Download className="mr-2 h-4 w-4" />
          Export Logbooks
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Filter className="h-5 w-5" />
            <span>Filters</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label htmlFor="student-search">Student Name</Label>
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  id="student-search"
                  placeholder="Search students..."
                  value={filters.student_name || ''}
                  onChange={(e) => setFilters(prev => ({ ...prev, student_name: e.target.value }))}
                  className="pl-8"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="status-filter">Status</Label>
              <Select 
                value={filters.status || 'all'} 
                onValueChange={(value) => setFilters(prev => ({ ...prev, status: value as any }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="overdue">Overdue</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="program-filter">Program</Label>
              <Input
                id="program-filter"
                placeholder="Filter by program..."
                value={filters.program || ''}
                onChange={(e) => setFilters(prev => ({ ...prev, program: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="supervisor-filter">Supervisor</Label>
              <Input
                id="supervisor-filter"
                placeholder="Filter by supervisor..."
                value={filters.supervisor || ''}
                onChange={(e) => setFilters(prev => ({ ...prev, supervisor: e.target.value }))}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Results Summary */}
      <div className="flex items-center justify-between">
        <p className="text-sm text-muted-foreground">
          Showing {filteredLogbooks.length} of {logbooks.length} logbook entries
        </p>
      </div>

      {/* Logbooks Table */}
      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Student</TableHead>
                <TableHead>Week</TableHead>
                <TableHead>Daily Logs</TableHead>
                <TableHead>Weekly Report</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredLogbooks.map((logbook) => {
                const weekStatus = getWeekStatus(logbook)
                const isExpanded = expandedStudent === `${logbook.student_id}-${logbook.week_number}`
                
                return (
                  <>
                    <TableRow key={`${logbook.student_id}-${logbook.week_number}`}>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <User className="h-4 w-4 text-muted-foreground" />
                          <span className="font-medium">{logbook.student_name}</span>
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span>Week {logbook.week_number}</span>
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <BookOpen className="h-4 w-4 text-muted-foreground" />
                          <span>{logbook.daily_logs.length}/6 days</span>
                          {logbook.daily_logs.length >= 6 ? (
                            <CheckCircle2 className="h-4 w-4 text-green-500" />
                          ) : (
                            <AlertCircle className="h-4 w-4 text-orange-500" />
                          )}
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          {logbook.weekly_report.is_submitted ? (
                            <>
                              <CheckCircle2 className="h-4 w-4 text-green-500" />
                              <span className="text-green-700">Submitted</span>
                            </>
                          ) : logbook.weekly_report.content ? (
                            <>
                              <Clock className="h-4 w-4 text-orange-500" />
                              <span className="text-orange-700">Draft</span>
                            </>
                          ) : (
                            <>
                              <AlertCircle className="h-4 w-4 text-gray-500" />
                              <span className="text-gray-700">Not Started</span>
                            </>
                          )}
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <Badge className={weekStatus.color}>
                          {weekStatus.status.replace('-', ' ')}
                        </Badge>
                      </TableCell>
                      
                      <TableCell>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => toggleStudentExpansion(`${logbook.student_id}-${logbook.week_number}`)}
                          className="h-8 w-8 p-0"
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                    
                    {/* Expanded Details */}
                    {isExpanded && (
                      <TableRow>
                        <TableCell colSpan={6} className="bg-muted/50">
                          <div className="p-4 space-y-4">
                            {/* Daily Logs Details */}
                            <div>
                              <h4 className="font-semibold mb-2">Daily Logs</h4>
                              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                                {logbook.daily_logs.map((log) => (
                                  <div key={log.id} className="p-2 bg-background rounded border">
                                    <div className="flex items-center justify-between">
                                      <span className="font-medium text-sm">{log.day_name}</span>
                                      <Badge variant="outline" className="text-xs">
                                        {log.status}
                                      </Badge>
                                    </div>
                                    <p className="text-xs text-muted-foreground mt-1">{log.title}</p>
                                    <p className="text-xs text-muted-foreground">{log.hours} hours</p>
                                  </div>
                                ))}
                              </div>
                            </div>
                            
                            {/* Weekly Report Details */}
                            {logbook.weekly_report.content && (
                              <div>
                                <h4 className="font-semibold mb-2">Weekly Report</h4>
                                <div className="p-3 bg-background rounded border">
                                  <div className="flex items-center justify-between mb-2">
                                    <span className="text-sm font-medium">
                                      {logbook.weekly_report.submission_type === 'upload' ? 'File Upload' : 'Written Report'}
                                    </span>
                                    <Badge variant={logbook.weekly_report.is_submitted ? "default" : "outline"}>
                                      {logbook.weekly_report.is_submitted ? 'Submitted' : 'Draft'}
                                    </Badge>
                                  </div>
                                  
                                  {logbook.weekly_report.submission_type === 'upload' && logbook.weekly_report.file_name ? (
                                    <p className="text-sm text-muted-foreground">
                                      File: {logbook.weekly_report.file_name}
                                    </p>
                                  ) : (
                                    <div 
                                      className="text-sm text-muted-foreground line-clamp-3"
                                      dangerouslySetInnerHTML={{ 
                                        __html: logbook.weekly_report.content?.substring(0, 200) + '...' || ''
                                      }}
                                    />
                                  )}
                                  
                                  {logbook.weekly_report.submitted_at && (
                                    <p className="text-xs text-muted-foreground mt-2">
                                      Submitted: {new Date(logbook.weekly_report.submitted_at).toLocaleString()}
                                    </p>
                                  )}
                                </div>
                              </div>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    )}
                  </>
                )
              })}
            </TableBody>
          </Table>
          
          {filteredLogbooks.length === 0 && (
            <div className="p-8 text-center text-muted-foreground">
              <BookOpen className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium mb-2">No logbooks found</p>
              <p className="text-sm">Try adjusting your filters or check back later</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
