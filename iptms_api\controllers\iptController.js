const IPT = require('../models/IPT');

const createIPT = async (req, res) => {
    const { name , user_id } = req.body;

    try {
        const result = await IPT.create(name, user_id);

        return res.status(200).json({
            success: true,
            message: 'IPT created successfully.',
            data: result 
        });

    } catch (err) {
        console.error(err.message);
        return res.status(500).json({
            success: false,
            message: 'Internal server error.'
        });
    }
};

const allIPTs = async (req, res) => {
    try {
        const ipts = await IPT.index();

        return res.status(200).json({
            success: true,
            message: 'IPT fetched successfully.',
            data: ipts 
        });

    } catch (err) {
        console.error(err.message);
        return res.status(500).json({
            success: false,
            message: 'Internal server error.'
        });
    }
};

module.exports = { createIPT , allIPTs };
