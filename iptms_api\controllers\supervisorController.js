const Supervisor = require('../models/Supervisor')

const supervisorCheckup = async (req, res) => {
    const { user_id, ipt_id } = req.user;
    
        try {
            const result = await Supervisor.init(user_id, ipt_id);
    
            return res.status(200).json({
                success: true,
                data: result 
            });
    
        } catch (err) {
            console.error(err.message);
            return res.status(500).json({
                success: false,
                message: err.message
            });
        }
}

const changePassword = async (req, res) => {
    const { password } = req.body;
    const { user_id, ipt_id } = req.user;
    
        try {
            await Supervisor.changePassword(user_id, ipt_id, password);
    
            return res.status(200).json({
                success: true,
                message: 'Password changed successfully',
            });
    
        } catch (err) {
            console.error(err.message);
            return res.status(500).json({
                success: false,
                message: 'Internal server error.'
            });
        }
}

const getAllSupervisors = async (req,res) => {
    try {
        const supervisors = await Supervisor.index();

        res.status(200).json({
            success: true,
            data: supervisors
        })
        
    } catch (error) {
        console.error(error)
        res.status(500).json({
            success: false,
            error: error
        })
    }
}

const getSingleSupervisor = async (req,res) => {
    const supervisor_id = req.params.id;
    try {
        const supervisor = await Supervisor.show(supervisor_id);

        res.status(200).json({
            success: true,
            data: supervisor
        })
        
    } catch (error) {
        console.error(error)
        res.status(500).json({
            success: false,
            error: error
        })
    }
}

const createSupervisor = async (req,res) => {
    const { first_name , last_name , email , phone  , ipt_id} = req.body;
    const { user_id } = req.user;
    try {
        await Supervisor.create(
            first_name , last_name , email , phone , user_id , ipt_id
        );

        res.status(200).json({
            success: true,
            message: 'Supervisor created successfully'
        })
        
    } catch (error) {
        res.status(500).json({
            success: false,
            message: error.message || 'Something went wrong'
        })
    }
}
const editSupervisor = async (req,res) => {
    const { first_name , last_name , email , phone  , ipt_id} = req.body;
    const supervisor_id = req.params.id;

    try {
        await Supervisor.update(
            first_name , last_name , email , phone , ipt_id , supervisor_id
        );

        res.status(200).json({
            success: true,
            message: 'Supervisor Updated successfully'
        })
        
    } catch (error) {
        res.status(500).json({
            success: false,
            message: error.message || 'Something went wrong'
        })
    }
}
const supervisorLogin = async (req,res) => {
    const { email , password} = req.body;
    try {
        const result = await Supervisor.login(
            email, password
        );

        res.status(200).json({
            success: true,
            message: 'OTP verified successfully',
            token: result.token
        })
        
    } catch (error) {
        res.status(500).json({
            success: false,
            message: error.message || 'Something went wrong'
        })
    }
}

const deleteSupervisor = async (req,res) => {
    const supervisor_id = req.params.id;
    try {
        await Supervisor.destroy(supervisor_id);

        res.status(200).json({
            success: true,
            message: "Supervisor deleted successfully"
        })
        
    } catch (error) {
        console.error(error)
        res.status(500).json({
            success: false,
            error: error
        })
    }
}

module.exports = {
    supervisorCheckup,
    getAllSupervisors,
    getSingleSupervisor,
    changePassword,
    createSupervisor,
    deleteSupervisor,
    supervisorLogin,
    editSupervisor
}