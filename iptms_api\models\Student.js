const db = require('../config/db');

// const ROLE_NAME = "student";

const index = async () => {
    try {
        const [rows] = await db.query(`
            SELECT 
                s.class,
                s.reg_no,
                p.name as program, 
                u.*
            FROM students s
            INNER JOIN users u on s.user_id = u.id 
            INNER JOIN programs p on s.program_id = p.id;
        `);
        return rows;
    } catch (err) {
        console.error(err.message);
        throw new Error('Internal server error.');
    }
}

const show = async (student_id) => {
    try {
        const [rows] = await db.query(`
            SELECT 
                s.class,
                s.reg_no,
                p.name as program, 
                u.*
            FROM students s
            INNER JOIN users u on s.user_id = u.id 
            INNER JOIN programs p on s.program_id = p.id
            WHERE u.id = ?;
        `,[student_id]);
        return rows;
    } catch (err) {
        console.error(err.message);
        throw new Error('Internal server error.');
    }
}

module.exports = {
    index,
    show
}